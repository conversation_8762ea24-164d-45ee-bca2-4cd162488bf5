'use client';
import React, { useEffect, useRef } from 'react';
import { timezones } from './timezones';
import { isMobile } from '@benzinga/device-utils';

let tvScriptLoadingPromise: Promise<unknown>;

// https://www.tradingview.com/widget/advanced-chart/

// Chart Style
// 0 - Bars
// 1 - Candles
// 9 - Hollow Candles
// 8 - Heiken Ashi
// 2 - Line
// 3 - Area
// 4 - Renko
// 7 - Line Break
// 5 - Kagi
// 6 - Point and Figure

interface TradingViewWidgetOptions {
  allow_symbol_change?: boolean;
  autosize?: boolean;
  container_id?: string;
  enable_publishing?: boolean;
  withdateranges?: boolean;
  hide_side_toolbar?: boolean;
  hide_top_toolbar?: boolean;
  hide_volume?: boolean;
  hide_legend?: boolean;
  save_image?: boolean;
  height?: number | string;
  width?: number | string;
  interval?: string;
  locale?: string; // en
  style?: number;
  backgroundColor?: string;
  details?: boolean;
  range?: '1D' | '5D' | '1M' | '3M' | '6M' | '12M' | 'YTD' | '1Y' | '5Y' | 'ALL'; // Default date range
  hotlist?: boolean;
  calendar?: string;
  show_popup_button?: boolean;
  popup_width?: number;
  popup_height?: number;
  symbol: string;
  theme?: 'light' | 'dark';
  timezone?: 'Etc/UTC' | 'exchange' | string;
}

declare interface TradingViewChartProto {
  remove(): void;
}

declare global {
  interface Window {
    TradingView: {
      widget: {
        prototype: TradingViewChartProto;
        new (opts: TradingViewWidgetOptions): TradingViewChartProto;
      };
    };
  }
}

export interface TradingViewWidgetProps {
  widgetProps: TradingViewWidgetOptions;
  customProps?: {
    mobileHeight?: number;
  };
}

export const TradingViewWidget: React.FC<TradingViewWidgetProps> = ({ customProps, widgetProps }) => {
  const onLoadScriptRef = useRef<() => void>(null);
  const tvId = useRef<string>(`tv_${Math.random().toString(36).substring(2, 10)}`);

  useEffect(() => {
    onLoadScriptRef.current = createWidget;

    if (!tvScriptLoadingPromise) {
      tvScriptLoadingPromise = new Promise(resolve => {
        const script = document.createElement('script');
        script.id = 'tradingview-widget-loading-script';
        script.src = 'https://s3.tradingview.com/tv.js';
        script.type = 'text/javascript';
        script.onload = resolve;

        document.head.appendChild(script);
      });
    }

    tvScriptLoadingPromise.then(() => onLoadScriptRef.current && onLoadScriptRef.current());

    const timezoneOffsetInHours = new Date().getTimezoneOffset() / 60;
    const timezoneOffsetNumber = timezoneOffsetInHours >= 0 ? -timezoneOffsetInHours : Math.abs(timezoneOffsetInHours);
    const timezone = timezones.find(timezone => timezone.offset === timezoneOffsetNumber)?.value;

    const getLocale = (locale: string | undefined): string => {
      if (locale === 'ko') {
        return 'kr';
      }

      return locale || 'en';
    };

    const height = isMobile() && customProps?.mobileHeight ? customProps.mobileHeight : widgetProps?.height || 600;

    function createWidget() {
      if (document.getElementById(`tradingview_${tvId.current}`) && 'TradingView' in window) {
        new window.TradingView.widget({
          allow_symbol_change: true,
          container_id: `tradingview_${tvId.current}`,
          enable_publishing: false,
          hide_side_toolbar: false,
          interval: 5,
          range: '1D',
          style: 2,
          theme: 'dark',
          timezone: timezone || 'exchange',
          width: '100%',
          withdateranges: true,
          ...widgetProps,
          height,
          locale: getLocale(widgetProps.locale),
        } as TradingViewWidgetOptions);
      }
    }

    return () => {
      onLoadScriptRef.current = null;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [widgetProps]);

  return (
    <div className="tradingview-widget-container">
      <div id={`tradingview_${tvId.current}`} />
    </div>
  );
};

export default TradingViewWidget;
